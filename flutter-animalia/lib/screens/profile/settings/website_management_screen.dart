import 'package:flutter/material.dart';

import '../../../l10n/app_localizations.dart';
import '../../../models/salon.dart';
import '../../../models/salon_settings.dart';
import '../../../services/salon_service.dart';
import '../../../services/settings_service.dart';
import '../../../services/ui_notification_service.dart';
import '../../../services/url_launcher_service.dart';
import '../../../widgets/common/standard_form_field.dart';
import '../../../widgets/permission_guard.dart';
import '../../../widgets/address_selection/location_selection_button.dart';

/// Screen for managing salon website settings with multi-step form
class WebsiteManagementScreen extends StatefulWidget {
  const WebsiteManagementScreen({super.key});

  @override
  State<WebsiteManagementScreen> createState() => _WebsiteManagementScreenState();
}

/// Enum for website management steps
enum WebsiteManagementStep {
  basicInfo,
  customizeBooking,
  businessHours,
  extraInfo,
  bookingAcceptance,
}

/// Policy options for cancellation/rescheduling
enum CancellationPolicy {
  hours24,
  hours48,
  hours72,
  noChanges,
}

/// Booking acceptance options
enum BookingAcceptance {
  automatic,
  manual,
}

class _WebsiteManagementScreenState extends State<WebsiteManagementScreen> {
  final _formKey = GlobalKey<FormState>();
  final PageController _pageController = PageController();

  // Current step
  WebsiteManagementStep _currentStep = WebsiteManagementStep.basicInfo;

  // Loading states
  bool _isLoading = false;
  bool _isSaving = false;
  SalonSettings? _currentSettings;
  Salon? _currentSalon;

  // Basic Info Controllers
  final _bookingWebsiteController = TextEditingController();
  final _businessNameController = TextEditingController();
  final _businessDescriptionController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _facebookController = TextEditingController();
  final _instagramController = TextEditingController();
  final _tiktokController = TextEditingController();

  // Business address
  String? _selectedAddress;

  // Business hours (simplified for now)
  Map<String, Map<String, String>> _businessHours = {
    'monday': {'open': '09:00', 'close': '18:00', 'isOpen': 'true'},
    'tuesday': {'open': '09:00', 'close': '18:00', 'isOpen': 'true'},
    'wednesday': {'open': '09:00', 'close': '18:00', 'isOpen': 'true'},
    'thursday': {'open': '09:00', 'close': '18:00', 'isOpen': 'true'},
    'friday': {'open': '09:00', 'close': '18:00', 'isOpen': 'true'},
    'saturday': {'open': '09:00', 'close': '16:00', 'isOpen': 'true'},
    'sunday': {'open': '10:00', 'close': '14:00', 'isOpen': 'false'},
  };

  // Extra info
  CancellationPolicy _cancellationPolicy = CancellationPolicy.hours24;

  // Booking acceptance
  BookingAcceptance _bookingAcceptance = BookingAcceptance.automatic;

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _bookingWebsiteController.dispose();
    _businessNameController.dispose();
    _businessDescriptionController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _facebookController.dispose();
    _instagramController.dispose();
    _tiktokController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load both salon settings and salon data in parallel
      final settingsResponse = await SettingsService.getSalonSettings();
      final salonResponse = await SalonService.getCurrentUserSalon();

      if (settingsResponse.success && settingsResponse.data != null) {
        _currentSettings = settingsResponse.data!;
      }

      if (salonResponse.success && salonResponse.data != null) {
        _currentSalon = salonResponse.data!;
      }

      _populateFormFields();
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('common.error'),
          message: 'Error loading settings: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _populateFormFields() {
    String businessName = '';

    // Populate from salon settings (primary source for contact info)
    if (_currentSettings != null) {
      businessName = _currentSettings!.name;
      _businessNameController.text = _currentSettings!.name;
      _phoneController.text = _currentSettings!.phone;
      _emailController.text = _currentSettings!.email;
      _selectedAddress = _currentSettings!.address.isNotEmpty ? _currentSettings!.address : null;
    }

    // Populate from salon data (for description and other fields not in settings)
    if (_currentSalon != null) {
      // Use salon name if settings name is empty
      if (_businessNameController.text.isEmpty) {
        businessName = _currentSalon!.name;
        _businessNameController.text = _currentSalon!.name;
      }

      // Use salon description
      if (_currentSalon!.description != null && _currentSalon!.description!.isNotEmpty) {
        _businessDescriptionController.text = _currentSalon!.description!;
      }

      // Use salon address if settings address is empty
      if (_selectedAddress == null || _selectedAddress!.isEmpty) {
        _selectedAddress = _currentSalon!.address.isNotEmpty ? _currentSalon!.address : null;
      }

      // Use salon phone if settings phone is empty
      if (_phoneController.text.isEmpty && _currentSalon!.phone != null) {
        _phoneController.text = _currentSalon!.phone!;
      }

      // Use salon email if settings email is empty
      if (_emailController.text.isEmpty && _currentSalon!.email != null) {
        _emailController.text = _currentSalon!.email!;
      }
    }

    // Generate booking website link using the business name
    if (businessName.isNotEmpty) {
      final formattedBusinessName = _formatBusinessNameForUrl(businessName);
      _bookingWebsiteController.text = 'https://animalia-programari.ro/$formattedBusinessName';
    }

    // TODO: Load business hours from settings when backend supports them
    // TODO: Load social media links when backend supports them
    // TODO: Load cancellation policy when backend supports them
    // TODO: Load booking acceptance settings when backend supports them
  }

  /// Format business name for URL by replacing spaces with underscores and making it URL-safe
  String _formatBusinessNameForUrl(String businessName) {
    return businessName
        .trim()
        .replaceAll(' ', '_')
        .replaceAll(RegExp(r'[^\w\-_]'), '') // Remove special characters except hyphens and underscores
        .toLowerCase();
  }

  // Navigation methods
  void _nextStep() {
    if (_currentStep.index < WebsiteManagementStep.values.length - 1) {
      setState(() {
        _currentStep = WebsiteManagementStep.values[_currentStep.index + 1];
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep.index > 0) {
      setState(() {
        _currentStep = WebsiteManagementStep.values[_currentStep.index - 1];
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToStep(WebsiteManagementStep step) {
    setState(() {
      _currentStep = step;
    });
    _pageController.animateToPage(
      step.index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _saveAllSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final response = await SettingsService.updateContactInfo(
        website: _bookingWebsiteController.text.trim(),
        phone: _phoneController.text.trim(),
        email: _emailController.text.trim(),
        address: _selectedAddress,
      );

      if (response.success) {
        if (mounted) {
          UINotificationService.showSuccess(
            context: context,
            title: context.tr('common.success'),
            message: context.tr('profile_screen.website_updated_success'),
          );
          Navigator.pop(context, true); // Return true to indicate success
        }
      } else {
        if (mounted) {
          UINotificationService.showError(
            context: context,
            title: context.tr('common.error'),
            message: response.error ?? 'Failed to update settings',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('common.error'),
          message: 'Error updating settings: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _openWebsite() async {
    final website = _bookingWebsiteController.text.trim();
    if (website.isEmpty) {
      UINotificationService.showError(
        context: context,
        title: context.tr('common.error'),
        message: context.tr('profile_screen.no_website_to_open'),
      );
      return;
    }

    String url = website;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    final success = await UrlLauncherService.openWebUrl(url);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, 'website');
    }
  }

  // Validation methods
  String? _validateWebsite(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Website is optional
    }

    final trimmed = value.trim();

    // Basic URL validation
    final urlPattern = RegExp(
      r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$',
      caseSensitive: false,
    );

    if (!urlPattern.hasMatch(trimmed)) {
      return context.tr('profile_screen.invalid_website_format');
    }

    return null;
  }

  String? _validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName ${context.tr('common.is_required')}';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Email is optional
    }

    final emailPattern = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailPattern.hasMatch(value.trim())) {
      return context.tr('profile_screen.invalid_email_format');
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return PermissionGuard.management(
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            context.tr('profile_screen.programming_website_title'),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          elevation: 0,
          actions: [
            if (_bookingWebsiteController.text.trim().isNotEmpty)
              IconButton(
                icon: const Icon(Icons.open_in_new),
                onPressed: _openWebsite,
                tooltip: context.tr('profile_screen.open_website'),
              ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  _buildStepIndicator(),
                  Expanded(
                    child: Form(
                      key: _formKey,
                      child: PageView(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentStep = WebsiteManagementStep.values[index];
                          });
                        },
                        children: [
                          _buildBasicInfoStep(),
                          _buildCustomizeBookingStep(),
                          _buildBusinessHoursStep(),
                          _buildExtraInfoStep(),
                          _buildBookingAcceptanceStep(),
                        ],
                      ),
                    ),
                  ),
                  _buildNavigationButtons(),
                ],
              ),
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: WebsiteManagementStep.values.map((step) {
          final isActive = step == _currentStep;
          final isCompleted = step.index < _currentStep.index;

          return Expanded(
            child: GestureDetector(
              onTap: () => _goToStep(step),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                child: Column(
                  children: [
                    Container(
                      height: 4,
                      decoration: BoxDecoration(
                        color: isActive || isCompleted
                            ? Theme.of(context).primaryColor
                            : Theme.of(context).dividerColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getStepTitle(step),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isActive
                            ? Theme.of(context).primaryColor
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                        fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  String _getStepTitle(WebsiteManagementStep step) {
    switch (step) {
      case WebsiteManagementStep.basicInfo:
        return context.tr('profile_screen.basic_info_step');
      case WebsiteManagementStep.customizeBooking:
        return context.tr('profile_screen.customize_booking_step');
      case WebsiteManagementStep.businessHours:
        return context.tr('profile_screen.business_hours_step');
      case WebsiteManagementStep.extraInfo:
        return context.tr('profile_screen.extra_info_step');
      case WebsiteManagementStep.bookingAcceptance:
        return context.tr('profile_screen.booking_acceptance_step');
    }
  }

  Widget _buildBasicInfoStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr('profile_screen.basic_info_title'),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          // Booking website link
          StandardFormField(
            controller: _bookingWebsiteController,
            labelText: context.tr('profile_screen.booking_website_label'),
            hintText: 'https://www.example.com',
            prefixIcon: Icons.language,
            validator: _validateWebsite,
            keyboardType: TextInputType.url,
          ),
          const SizedBox(height: 16),

          // Business name
          StandardFormField(
            controller: _businessNameController,
            labelText: context.tr('profile_screen.business_name_label'),
            hintText: context.tr('profile_screen.business_name_hint'),
            prefixIcon: Icons.business,
            validator: (value) => _validateRequired(value, context.tr('profile_screen.business_name_label')),
          ),
          const SizedBox(height: 16),

          // Business description
          StandardFormField(
            controller: _businessDescriptionController,
            labelText: context.tr('profile_screen.business_description_label'),
            hintText: context.tr('profile_screen.business_description_hint'),
            prefixIcon: Icons.description,
            maxLines: 3,
          ),
          const SizedBox(height: 24),

          // Business address
          Text(
            context.tr('profile_screen.business_address_label'),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          LocationSelectionButton(
            selectedAddress: _selectedAddress,
            hint: context.tr('profile_screen.select_business_address'),
            onLocationSelected: (location, address) {
              setState(() {
                _selectedAddress = address;
              });
            },
          ),
          const SizedBox(height: 24),

          // Contact information
          Text(
            context.tr('profile_screen.contact_information_label'),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          StandardFormField(
            controller: _phoneController,
            labelText: context.tr('profile_screen.phone_label'),
            hintText: context.tr('profile_screen.phone_hint'),
            prefixIcon: Icons.phone,
            keyboardType: TextInputType.phone,
          ),
          const SizedBox(height: 16),

          StandardFormField(
            controller: _emailController,
            labelText: context.tr('profile_screen.email_label'),
            hintText: context.tr('profile_screen.email_hint'),
            prefixIcon: Icons.email,
            keyboardType: TextInputType.emailAddress,
            validator: _validateEmail,
          ),
          const SizedBox(height: 16),

          StandardFormField(
            controller: _facebookController,
            labelText: context.tr('profile_screen.facebook_label'),
            hintText: 'https://facebook.com/yourpage',
            prefixIcon: Icons.facebook,
            keyboardType: TextInputType.url,
          ),
          const SizedBox(height: 16),

          StandardFormField(
            controller: _instagramController,
            labelText: context.tr('profile_screen.instagram_label'),
            hintText: 'https://instagram.com/yourpage',
            prefixIcon: Icons.camera_alt,
            keyboardType: TextInputType.url,
          ),
          const SizedBox(height: 16),

          StandardFormField(
            controller: _tiktokController,
            labelText: context.tr('profile_screen.tiktok_label'),
            hintText: 'https://tiktok.com/@yourpage',
            prefixIcon: Icons.music_note,
            keyboardType: TextInputType.url,
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildCustomizeBookingStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr('profile_screen.customize_booking_title'),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Icons.construction,
                    size: 64,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('profile_screen.work_in_progress'),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.tr('profile_screen.customize_booking_description'),
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessHoursStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr('profile_screen.business_hours_title'),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          ..._businessHours.entries.map((entry) {
            final day = entry.key;
            final hours = entry.value;
            final isOpen = hours['isOpen'] == 'true';

            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        context.tr('profile_screen.day_$day'),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    Expanded(
                      child: Switch(
                        value: isOpen,
                        onChanged: (value) {
                          setState(() {
                            _businessHours[day]!['isOpen'] = value.toString();
                          });
                        },
                      ),
                    ),
                    if (isOpen) ...[
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${hours['open']} - ${hours['close']}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () => _editBusinessHours(day, hours),
                      ),
                    ] else
                      Expanded(
                        flex: 3,
                        child: Text(
                          context.tr('profile_screen.closed'),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildExtraInfoStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr('profile_screen.extra_info_title'),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          Text(
            context.tr('profile_screen.cancellation_policy_label'),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          ...CancellationPolicy.values.map((policy) {
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: RadioListTile<CancellationPolicy>(
                title: Text(_getCancellationPolicyText(policy)),
                value: policy,
                groupValue: _cancellationPolicy,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _cancellationPolicy = value;
                    });
                  }
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildBookingAcceptanceStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr('profile_screen.booking_acceptance_title'),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          ...BookingAcceptance.values.map((acceptance) {
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: RadioListTile<BookingAcceptance>(
                title: Text(_getBookingAcceptanceText(acceptance)),
                subtitle: Text(_getBookingAcceptanceDescription(acceptance)),
                value: acceptance,
                groupValue: _bookingAcceptance,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _bookingAcceptance = value;
                    });
                  }
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          if (_currentStep.index > 0)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _previousStep,
                icon: const Icon(Icons.arrow_back),
                label: Text(context.tr('common.previous')),
              ),
            ),
          if (_currentStep.index > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isSaving ? null : () {
                if (_currentStep.index < WebsiteManagementStep.values.length - 1) {
                  _nextStep();
                } else {
                  _saveAllSettings();
                }
              },
              icon: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Icon(_currentStep.index < WebsiteManagementStep.values.length - 1
                      ? Icons.arrow_forward
                      : Icons.save),
              label: Text(
                _isSaving
                    ? context.tr('common.saving')
                    : _currentStep.index < WebsiteManagementStep.values.length - 1
                        ? context.tr('common.next')
                        : context.tr('common.save'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  void _editBusinessHours(String day, Map<String, String> hours) {
    // TODO: Implement time picker dialog
    // For now, this is a placeholder
  }

  String _getCancellationPolicyText(CancellationPolicy policy) {
    switch (policy) {
      case CancellationPolicy.hours24:
        return context.tr('profile_screen.cancellation_24_hours');
      case CancellationPolicy.hours48:
        return context.tr('profile_screen.cancellation_48_hours');
      case CancellationPolicy.hours72:
        return context.tr('profile_screen.cancellation_72_hours');
      case CancellationPolicy.noChanges:
        return context.tr('profile_screen.cancellation_no_changes');
    }
  }

  String _getBookingAcceptanceText(BookingAcceptance acceptance) {
    switch (acceptance) {
      case BookingAcceptance.automatic:
        return context.tr('profile_screen.booking_automatic');
      case BookingAcceptance.manual:
        return context.tr('profile_screen.booking_manual');
    }
  }

  String _getBookingAcceptanceDescription(BookingAcceptance acceptance) {
    switch (acceptance) {
      case BookingAcceptance.automatic:
        return context.tr('profile_screen.booking_automatic_description');
      case BookingAcceptance.manual:
        return context.tr('profile_screen.booking_manual_description');
    }
  }
}
