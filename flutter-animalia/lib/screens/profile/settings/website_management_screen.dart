import 'package:flutter/material.dart';

import '../../../l10n/app_localizations.dart';
import '../../../models/salon_settings.dart';
import '../../../services/settings_service.dart';
import '../../../services/ui_notification_service.dart';
import '../../../services/url_launcher_service.dart';
import '../../../widgets/common/standard_form_field.dart';
import '../../../widgets/permission_guard.dart';

/// Screen for managing salon website settings
class WebsiteManagementScreen extends StatefulWidget {
  const WebsiteManagementScreen({super.key});

  @override
  State<WebsiteManagementScreen> createState() => _WebsiteManagementScreenState();
}

class _WebsiteManagementScreenState extends State<WebsiteManagementScreen> {
  final _formKey = GlobalKey<FormState>();
  final _websiteController = TextEditingController();
  
  bool _isLoading = false;
  bool _isSaving = false;
  SalonSettings? _currentSettings;

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  @override
  void dispose() {
    _websiteController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await SettingsService.getSalonSettings();
      if (response.success && response.data != null) {
        _currentSettings = response.data!;
        _websiteController.text = _currentSettings!.website;
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('common.error'),
          message: 'Error loading settings: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveWebsite() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final response = await SettingsService.updateContactInfo(
        website: _websiteController.text.trim(),
      );

      if (response.success) {
        if (mounted) {
          UINotificationService.showSuccess(
            context: context,
            title: context.tr('common.success'),
            message: context.tr('profile_screen.website_updated_success'),
          );
          Navigator.pop(context, true); // Return true to indicate success
        }
      } else {
        if (mounted) {
          UINotificationService.showError(
            context: context,
            title: context.tr('common.error'),
            message: response.error ?? 'Failed to update website',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('common.error'),
          message: 'Error updating website: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _openWebsite() async {
    final website = _websiteController.text.trim();
    if (website.isEmpty) {
      UINotificationService.showError(
        context: context,
        title: context.tr('common.error'),
        message: context.tr('profile_screen.no_website_to_open'),
      );
      return;
    }

    String url = website;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    final success = await UrlLauncherService.openWebUrl(url);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, 'website');
    }
  }

  String? _validateWebsite(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Website is optional
    }

    final trimmed = value.trim();
    
    // Basic URL validation
    final urlPattern = RegExp(
      r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$',
      caseSensitive: false,
    );
    
    if (!urlPattern.hasMatch(trimmed)) {
      return context.tr('profile_screen.invalid_website_format');
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return PermissionGuard.management(
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            context.tr('profile_screen.programming_website_title'),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          elevation: 0,
          actions: [
            if (_websiteController.text.trim().isNotEmpty)
              IconButton(
                icon: const Icon(Icons.open_in_new),
                onPressed: _openWebsite,
                tooltip: context.tr('profile_screen.open_website'),
              ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoCard(),
                      const SizedBox(height: 24),
                      _buildWebsiteForm(),
                      const SizedBox(height: 32),
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('profile_screen.website_info_title'),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              context.tr('profile_screen.website_info_description'),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWebsiteForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('profile_screen.website_url_label'),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        StandardFormField(
          controller: _websiteController,
          labelText: context.tr('profile_screen.website_url_hint'),
          hintText: 'https://www.example.com',
          prefixIcon: Icons.language,
          validator: _validateWebsite,
          keyboardType: TextInputType.url,
          onFieldSubmitted: (_) => _saveWebsite(),
        ),
        const SizedBox(height: 8),
        Text(
          context.tr('profile_screen.website_url_help'),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isSaving ? null : _saveWebsite,
            icon: _isSaving
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.save),
            label: Text(
              _isSaving
                  ? context.tr('common.saving')
                  : context.tr('common.save'),
            ),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        if (_websiteController.text.trim().isNotEmpty) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _openWebsite,
              icon: const Icon(Icons.open_in_new),
              label: Text(context.tr('profile_screen.open_website')),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
